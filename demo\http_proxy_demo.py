#!/usr/bin/env python3
"""
HTTP 代理测试 Demo
"""

import asyncio
import json
import sys
import zendriver as zd


async def test_http_proxy():
    """测试 HTTP 代理配置"""
    
    # ==================== 代理配置 ====================
    # 使用你提供的 HTTP 代理
    PROXY_URL = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:10018"
    
    print("🌐 HTTP 代理测试")
    print("-" * 30)
    
    try:
        print(f"🔐 代理: {PROXY_URL}")
        print()
        
        # 启动浏览器
        browser = await zd.start(
            headless=True,
            browser_args=[f"--proxy-server={PROXY_URL}"],
            browser_connection_timeout=10.0  # 增加超时时间
        )
        
        # 检查 IP 地址
        print("📍 检查 IP 地址...")
        tab = await browser.get("https://httpbin.org/ip")
        await tab.wait_for_ready_state("complete")
        
        ip_text = await tab.evaluate("document.body.innerText")
        ip_data = json.loads(ip_text)
        
        print(f"当前 IP: {ip_data['origin']}")
        print("✅ HTTP 代理连接成功！")
        
        await browser.stop()
        
    except Exception as e:
        print(f"❌ 代理连接失败: {e}")
        print("💡 请检查代理服务器是否运行正常")


if __name__ == "__main__":
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(test_http_proxy())
