# Zendriver SOCKS 代理和 User-Agent 测试工具 🐱

这是一个专门用于测试 Zendriver 中 SOCKS 代理配置和 User-Agent 设置的完整工具包。

## 📁 文件说明

### 1. `proxy_ua_test_demo.py` - 完整测试套件
- 🔍 **功能全面**: 包含所有测试场景
- 🧪 **多种测试**: 无代理、SOCKS代理（带/不带认证）、多种UA测试
- 📊 **详细报告**: 完整的测试结果和错误信息
- 🎯 **适合场景**: 深度测试和问题诊断

### 2. `quick_proxy_test.py` - 快速测试工具
- ⚡ **快速验证**: 简化的测试流程
- 🔧 **易于配置**: 顶部配置区域，修改方便
- 🎮 **交互式**: 支持选择不同测试模式
- 🎯 **适合场景**: 日常验证和快速检查

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install zendriver
```

### 2. 配置代理信息
编辑 `quick_proxy_test.py` 中的配置：

```python
PROXY_CONFIG = {
    'enabled': True,           # 是否启用代理测试
    'host': '127.0.0.1',      # 你的代理服务器地址
    'port': '1080',           # 你的代理端口
    'username': 'your_user',  # 用户名（无认证时留空）
    'password': 'your_pass'   # 密码（无认证时留空）
}
```

### 3. 运行测试
```bash
# 快速测试
python quick_proxy_test.py

# 完整测试
python proxy_ua_test_demo.py
```

## 🔧 配置说明

### SOCKS 代理配置

#### 无认证代理
```python
browser_args = ["--proxy-server=socks5://127.0.0.1:1080"]
```

#### 带认证代理
```python
from urllib.parse import quote

username = quote("your_username")  # URL编码特殊字符
password = quote("your_password")
proxy_url = f"socks5://{username}:{password}@127.0.0.1:1080"
browser_args = [f"--proxy-server={proxy_url}"]
```

### User-Agent 配置

#### 启动时设置（推荐）
```python
browser = await zd.start(
    user_agent="Your Custom User Agent String"
)
```

#### 运行时设置
```python
await tab.set_user_agent(
    "Your Custom User Agent",
    accept_language="zh-CN",
    platform="Win32"
)
```

## 🧪 测试场景

### 1. 基础连接测试
- ✅ 检查代理连接是否成功
- ✅ 验证 IP 地址变化
- ✅ 测试网络延迟

### 2. User-Agent 验证
- ✅ 验证 UA 字符串是否正确设置
- ✅ 测试不同浏览器 UA 模拟
- ✅ 检查请求头完整性

### 3. 代理认证测试
- ✅ 无认证 SOCKS 代理
- ✅ 用户名密码认证
- ✅ 特殊字符处理

### 4. 错误处理测试
- ✅ 代理连接失败
- ✅ 认证失败
- ✅ 网络超时

## 📊 测试结果示例

```
🚀 开始快速测试...
🔧 代理配置: 启用
🌐 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)...
------------------------------------------------------------
🔐 使用认证代理: testuser@127.0.0.1:1080
🌟 启动浏览器...
🔍 检查 IP 地址...
✅ 当前 IP: ***********
🔍 检查请求头...
✅ 请求头信息:
   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 ZendriverTest/1.0
   Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
   Accept-Language: en-US,en;q=0.5
✅ User-Agent 设置成功！
🎉 测试完成！
```

## 🛠️ 常见问题

### Q1: 代理连接失败
**A**: 检查以下项目：
- 代理服务器是否运行
- 地址和端口是否正确
- 防火墙设置
- 网络连接

### Q2: 认证失败
**A**: 确认：
- 用户名密码正确
- 特殊字符已正确编码
- 代理服务器支持认证

### Q3: User-Agent 未生效
**A**: 尝试：
- 使用启动时设置而非运行时设置
- 检查 UA 字符串格式
- 验证浏览器参数

### Q4: 测试网站无法访问
**A**: 可以替换测试网站：
```python
TEST_URLS = {
    'ip': 'https://api.ipify.org?format=json',
    'headers': 'https://httpbin.org/headers'
}
```

## 🔍 调试技巧

### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 使用有头模式调试
```python
browser = await zd.start(
    headless=False,  # 显示浏览器窗口
    # ... 其他配置
)
```

### 3. 增加超时时间
```python
browser = await zd.start(
    browser_connection_timeout=10.0,  # 增加到10秒
    # ... 其他配置
)
```

## 📝 自定义扩展

### 添加新的测试网站
```python
# 在测试类中添加
self.test_urls['custom'] = 'https://your-test-site.com/api'
```

### 添加新的 User-Agent
```python
self.test_user_agents['custom'] = 'Your Custom UA String'
```

### 添加代理类型测试
```python
# HTTP 代理测试
proxy_url = f"http://{username}:{password}@{host}:{port}"
browser_args = [f"--proxy-server={proxy_url}"]
```

## 🎯 最佳实践

1. **安全性**: 不要在代码中硬编码密码，使用环境变量
2. **性能**: 使用无头模式进行自动化测试
3. **稳定性**: 设置合适的超时时间
4. **兼容性**: 在 Windows 上使用推荐的事件循环策略

## 📞 技术支持

如果遇到问题，请检查：
1. Zendriver 版本是否最新
2. Python 版本兼容性
3. 网络环境配置
4. 代理服务器状态

---

**作者**: 小喵 🐱  
**版本**: 1.0  
**更新**: 2024年
