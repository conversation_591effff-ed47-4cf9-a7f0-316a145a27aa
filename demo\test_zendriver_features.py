#!/usr/bin/env python3
"""
Zendriver 功能测试 - pytest 版本
"""

import asyncio
import json
import sys
import requests
import pytest
import zendriver as zd


def get_current_ip():
    """使用 requests 获取当前 IP 地址"""
    try:
        response = requests.get("https://httpbin.org/ip", timeout=10)
        return response.json()["origin"]
    except Exception as e:
        return f"获取失败: {e}"


@pytest.mark.asyncio
async def test_user_agent_only():
    """测试 User-Agent 设置（无代理）"""
    
    custom_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 ZendriverTest/1.0"
    
    print(f"\n🎭 测试 User-Agent: {custom_ua}")
    
    # 启动浏览器
    browser = await zd.start(
        headless=False,
        user_agent=custom_ua,
        browser_connection_timeout=15.0
    )

    print("⏳ 浏览器已启动，等待 2 秒...")
    await asyncio.sleep(2)
    
    try:
        # 测试 User-Agent
        tab = await browser.get("https://httpbin.org/user-agent")
        print("⏳ 页面加载中...")
        await asyncio.sleep(2)
        await tab.wait_for_ready_state("complete")
        print("⏳ 页面加载完成，等待 1 秒...")
        await asyncio.sleep(1)
        
        ua_text = await tab.evaluate("document.body.innerText")
        ua_data = json.loads(ua_text)
        detected_ua = ua_data['user-agent']
        
        print(f"   设置的 UA: {custom_ua}")
        print(f"   检测到的 UA: {detected_ua}")
        
        # 断言 User-Agent 设置成功
        assert custom_ua == detected_ua, f"User-Agent 不匹配: 期望 {custom_ua}, 实际 {detected_ua}"
        print("   ✅ User-Agent 测试通过")
        
    finally:
        await browser.stop()


@pytest.mark.asyncio
async def test_ip_check_no_proxy():
    """测试无代理情况下的 IP 获取"""
    
    print(f"\n📍 测试无代理 IP 获取")
    
    # 获取本机 IP 作为对比
    original_ip = get_current_ip()
    print(f"   requests 获取的 IP: {original_ip}")
    
    # 启动浏览器
    browser = await zd.start(
        headless=False,
        browser_connection_timeout=15.0
    )
    
    try:
        # 测试 IP 地址
        tab = await browser.get("https://httpbin.org/ip")
        await tab.wait_for_ready_state("complete")
        
        ip_text = await tab.evaluate("document.body.innerText")
        ip_data = json.loads(ip_text)
        browser_ip = ip_data['origin']
        
        print(f"   浏览器获取的 IP: {browser_ip}")
        
        # 断言 IP 应该一致（无代理情况下）
        assert browser_ip == original_ip, f"IP 不一致: requests={original_ip}, browser={browser_ip}"
        print("   ✅ IP 获取测试通过")
        
    finally:
        await browser.stop()


@pytest.mark.asyncio
async def test_proxy_connection():
    """测试代理连接（预期失败）"""
    
    proxy_url = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:10018"
    
    print(f"\n🌐 测试代理连接: {proxy_url}")
    print("   注意: 此测试预期失败，用于验证代理问题")
    
    # 启动浏览器
    browser = await zd.start(
        headless=False,
        browser_args=[f"--proxy-server={proxy_url}"],
        browser_connection_timeout=15.0
    )

    print("⏳ 代理浏览器已启动，等待 3 秒方便观察...")
    await asyncio.sleep(3)
    
    try:
        # 尝试访问测试页面
        tab = await browser.get("https://httpbin.org/ip")
        print("⏳ 代理页面加载中，等待 3 秒观察...")
        await asyncio.sleep(3)
        await tab.wait_for_ready_state("complete")
        print("⏳ 代理页面加载完成，等待 2 秒...")
        await asyncio.sleep(2)
        
        # 获取页面内容
        page_content = await tab.evaluate("document.body.innerText")
        print(f"   页面内容: {repr(page_content[:100])}")
        
        # 检查是否是错误页面
        if "ERR_NO_SUPPORTED_PROXIES" in page_content or "无法访问此网站" in page_content:
            print("   ⚠️  代理连接失败（预期结果）")
            pytest.skip("代理服务器不可用，跳过此测试")
        else:
            # 如果成功，尝试解析 JSON
            try:
                ip_data = json.loads(page_content)
                proxy_ip = ip_data['origin']
                print(f"   ✅ 代理工作正常，IP: {proxy_ip}")
            except json.JSONDecodeError:
                print("   ❌ 页面内容不是有效的 JSON")
                pytest.fail("代理返回了非 JSON 内容")
        
    finally:
        await browser.stop()


@pytest.mark.asyncio
async def test_combined_ua_and_ip():
    """综合测试：User-Agent + IP 检查"""
    
    custom_ua = "ZendriverPytest/1.0 (Combined Test)"
    
    print(f"\n🔄 综合测试")
    print(f"   User-Agent: {custom_ua}")
    
    browser = await zd.start(
        headless=False,
        user_agent=custom_ua,
        browser_connection_timeout=15.0
    )
    
    try:
        # 测试 1: IP 检查
        tab = await browser.get("https://httpbin.org/ip")
        await tab.wait_for_ready_state("complete")
        
        ip_text = await tab.evaluate("document.body.innerText")
        ip_data = json.loads(ip_text)
        browser_ip = ip_data['origin']
        
        # 测试 2: User-Agent 检查
        await tab.get("https://httpbin.org/user-agent")
        await tab.wait_for_ready_state("complete")
        
        ua_text = await tab.evaluate("document.body.innerText")
        ua_data = json.loads(ua_text)
        detected_ua = ua_data['user-agent']
        
        print(f"   IP: {browser_ip}")
        print(f"   UA: {detected_ua}")
        
        # 断言
        assert custom_ua == detected_ua, "User-Agent 设置失败"
        assert browser_ip, "IP 获取失败"
        
        print("   ✅ 综合测试通过")
        
    finally:
        await browser.stop()


if __name__ == "__main__":
    # 设置 Windows 兼容性
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
