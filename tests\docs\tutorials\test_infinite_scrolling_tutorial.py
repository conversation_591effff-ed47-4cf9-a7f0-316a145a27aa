from unittest.mock import Mock

from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from tests.docs import import_from_path


async def test_infinite_scrolling_tutorial_1(
    mocker: <PERSON><PERSON><PERSON><PERSON><PERSON>, mock_print: Mock, mock_start: Mock
) -> None:
    module = import_from_path("docs/tutorials/tutorial-code/infinite-scrolling-1.py")

    await module.main()

    mock_print.assert_called_once_with([])


async def test_infinite_scrolling_tutorial_2(
    mocker: <PERSON><PERSON><PERSON>ixture, mock_print: Mock, mock_start: Mock
) -> None:
    module = import_from_path("docs/tutorials/tutorial-code/infinite-scrolling-2.py")

    await module.main()

    mock_print.assert_has_calls(
        [mocker.call(f"Card {i}") for i in range(1, 11)],
    )


async def test_infinite_scrolling_tutorial_3(
    mocker: Mo<PERSON>Fixture, mock_print: Mock, mock_start: Mock
) -> None:
    module = import_from_path("docs/tutorials/tutorial-code/infinite-scrolling-3.py")

    await module.main()

    mock_print.assert_has_calls(
        [
            mocker.call("Loaded new cards. Current count:", 10),
            mocker.call("Loaded new cards. Current count:", 20),
            mocker.call("Loaded new cards. Current count:", 30),
            mocker.call("Lucky card found: Card 27"),
        ]
    )
