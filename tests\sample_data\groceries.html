<!DOCTYPE html>
<html>
  <head>
    <title>Grocery List</title>
      <script>
        function downloadGroceryList() {
            const groceries = [
                "Apples (42)",
                "Bananas",
                "Carrots",
                "Donuts",
                "Eggs",
                "French Fries",
                "Grapes"
            ];

            const blob = new Blob([groceries.join('\n')], { type: 'text/plain' });
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = 'grocery_list.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
    </script>
  </head>

    <body>
        <h1>Grocery List</h1>
        <ul>
        <li aria-label="Apples (42)">Apples</li>
        <li>Bananas</li>
        <li>Carrots</li>
        <li>Donuts</li>
        <li>Eggs</li>
        <li>French Fries</li>
        <li>Grapes</li>
        </ul>
        <button id="download_file" onclick="downloadGroceryList()">Download List</button>
    </body>
</html>
