name: test
on:
  push:
    branches:
      - main
    paths:
      - "**.py"
  pull_request:
    branches:
      - main
    paths:
      - "**.py"
  workflow_dispatch:
jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest, windows-latest ]
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v3
      - name: Set up Python
        run: uv python install
      - name: Install dependencies
        run: uv sync --all-extras --dev
      - name: Run tests
        env:
          ZENDRIVER_TEST_BROWSERS: "headless"
          ZENDRIVER_TEST_NO_SANDBOX: "true"
        shell: bash
        run: ./scripts/test.sh
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
