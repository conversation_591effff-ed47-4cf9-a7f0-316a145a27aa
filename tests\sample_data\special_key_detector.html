<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Escape Key mainpage Test</title>
    <style>
        body {
        margin: 0;
        padding: 2rem;
        font-family: Arial, sans-serif;
        font-size: 1.2rem;
        background-color: #fff;
        color: #000;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }

        #mainpageButton {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px;
        }

        #mainpageButton:hover {
            background: #0056b3;
        }

        /* Modal/mainpage styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            width: 400px;
            max-width: 90%;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .closeButton {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
        }

        .closeButton:hover {
            color: #000;
        }

        .mainpage-title {
            color: #333;
            margin-bottom: 20px;
        }

        .mainpage-text {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .popUp-footer {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #888;
            font-size: 14px;
        }

        /* Status indicator */
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            background: #ffffff;
        }

        .status.popUp-open {
            background: #ffffff;
        }

        .status.popUp-closed {
            background: #ffffff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Escape Key popUp Test</h1>
        <p>Click the button below to open a popUp. Press <strong>Escape</strong> to close it.</p>
        
        <button id="mainpageButton">Open popUp</button>
        
        <div id="status" class="status">Ready - Click button to open popUp</div>
    </div>


    <div id="mainpage" class="modal">
        <div class="modal-content">
            <h2 class="mainpage-title">Test popUp</h2>
            <p class="mainpage-text">
                This is a test popUp that can be closed with the Escape key.
            </p>
            <div class="popUp-footer">
                Press <strong>Escape</strong> to close this popUp
            </div>
        </div>
    </div>

    <script>
        const mainpage = document.getElementById('mainpage');
        const openButton = document.getElementById('mainpageButton');
        const status = document.getElementById('status');

        // Track popUp state
        let isPopUpOpen = false;

        function openPopUp() {
            mainpage.style.display = 'block';
            isPopUpOpen = true;
            status.textContent = 'popUp is OPEN - Press Escape to close';
            status.className = 'status popUp-open';

            // Focus the popUp for better keyboard interaction
            mainpage.focus();

            window.popUpState = 'open';
        }

        function closePopUp() {
            mainpage.style.display = 'none';
            isPopUpOpen = false;
            status.textContent = 'popUp is CLOSED - Click button to open again';
            status.className = 'status popUp-closed';
            
            // Return focus to the open button
            openButton.focus();

            window.isPopUpOpen = 'closed';
        }

        // Open popUp when button is clicked
        openButton.addEventListener('click', openPopUp);

        // Close popUp when Escape key is pressed
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' || event.keyCode === 27) {
                if (isPopUpOpen) {
                    closePopUp();
                    event.preventDefault(); // Prevent other escape behaviors
                }
            }
        });

        // Functions for zendriver testing
        window.openTestmainpage = openPopUp;
        window.closeTestmainpage = closePopUp;
        window.ismainpageOpen = () => ismainpageOpen;
        window.getmainpageState = () => window.mainpageState || 'closed';
        
        // Wait for mainpage to open/close
        window.waitFormainpageState = (expectedState, timeout = 5000) => {
            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                const checkState = () => {
                    const currentState = ismainpageOpen ? 'open' : 'closed';
                    if (currentState === expectedState) {
                        resolve(currentState);
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error(`Timeout waiting for mainpage state: ${expectedState}`));
                    } else {
                        setTimeout(checkState, 100);
                    }
                };
                checkState();
            });
        };

        window.mainpageState = 'closed';
    </script>
</body>
</html>