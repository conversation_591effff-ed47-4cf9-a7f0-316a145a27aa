#!/usr/bin/env python3
"""
Zendriver SOCKS 代理和 User-Agent 测试 Demo
作者: 小喵 🐱
功能: 测试 SOCKS 代理配置和 User-Agent 设置
"""

import asyncio
import json
import sys
from urllib.parse import quote
from typing import Optional, Dict, Any
import zendriver as zd


class ProxyUATestDemo:
    """代理和UA测试演示类"""
    
    def __init__(self):
        self.test_urls = {
            'ip_check': 'https://httpbin.org/ip',
            'headers_check': 'https://httpbin.org/headers',
            'user_agent_check': 'https://httpbin.org/user-agent'
        }
        
        # 测试用的 User-Agent 列表
        self.test_user_agents = {
            'chrome_windows': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'firefox_linux': 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'safari_mac': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'mobile_android': 'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            'custom_bot': 'ZendriverBot/1.0 (Testing Purpose Only)'
        }

    def print_section(self, title: str):
        """打印分节标题"""
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print('='*60)

    def print_result(self, label: str, data: Any, success: bool = True):
        """打印测试结果"""
        status = "✅" if success else "❌"
        print(f"{status} {label}:")
        if isinstance(data, dict):
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"   {data}")
        print()

    async def test_without_proxy(self, user_agent: str = None):
        """测试不使用代理的情况"""
        self.print_section("测试 1: 不使用代理")
        
        try:
            browser_args = []
            config_info = {"proxy": "无", "user_agent": user_agent or "默认"}
            
            browser = await zd.start(
                headless=True,
                user_agent=user_agent,
                browser_args=browser_args
            )
            
            print(f"📋 配置信息: {config_info}")
            
            tab = await browser.get(self.test_urls['ip_check'])
            await tab.wait_for_ready_state("complete")
            
            # 获取 IP 信息
            ip_text = await tab.evaluate("document.body.innerText")
            ip_data = json.loads(ip_text)
            self.print_result("当前 IP 地址", ip_data)
            
            # 测试 User-Agent
            await tab.get(self.test_urls['user_agent_check'])
            await tab.wait_for_ready_state("complete")
            ua_text = await tab.evaluate("document.body.innerText")
            ua_data = json.loads(ua_text)
            self.print_result("User-Agent", ua_data)
            
            await browser.stop()
            return True
            
        except Exception as e:
            self.print_result("测试失败", str(e), False)
            return False

    async def test_socks_proxy_no_auth(self, proxy_host: str, proxy_port: str, user_agent: str = None):
        """测试不需要认证的 SOCKS 代理"""
        self.print_section("测试 2: SOCKS 代理 (无认证)")
        
        try:
            proxy_url = f"socks5://{proxy_host}:{proxy_port}"
            browser_args = [f"--proxy-server={proxy_url}"]
            
            config_info = {
                "proxy": f"SOCKS5 {proxy_host}:{proxy_port}",
                "auth": "无",
                "user_agent": user_agent or "默认"
            }
            
            browser = await zd.start(
                headless=True,
                user_agent=user_agent,
                browser_args=browser_args,
                browser_connection_timeout=5.0  # 增加超时时间
            )
            
            print(f"📋 配置信息: {config_info}")
            
            tab = await browser.get(self.test_urls['ip_check'])
            await tab.wait_for_ready_state("complete")
            
            # 获取 IP 信息
            ip_text = await tab.evaluate("document.body.innerText")
            ip_data = json.loads(ip_text)
            self.print_result("代理后的 IP 地址", ip_data)
            
            # 测试 Headers
            await tab.get(self.test_urls['headers_check'])
            await tab.wait_for_ready_state("complete")
            headers_text = await tab.evaluate("document.body.innerText")
            headers_data = json.loads(headers_text)
            self.print_result("请求头信息", headers_data['headers'])
            
            await browser.stop()
            return True
            
        except Exception as e:
            self.print_result("SOCKS 代理测试失败", str(e), False)
            return False

    async def test_socks_proxy_with_auth(self, proxy_host: str, proxy_port: str, 
                                       username: str, password: str, user_agent: str = None):
        """测试需要认证的 SOCKS 代理"""
        self.print_section("测试 3: SOCKS 代理 (带认证)")
        
        try:
            # URL 编码用户名和密码中的特殊字符
            encoded_username = quote(username)
            encoded_password = quote(password)
            proxy_url = f"socks5://{encoded_username}:{encoded_password}@{proxy_host}:{proxy_port}"
            
            browser_args = [f"--proxy-server={proxy_url}"]
            
            config_info = {
                "proxy": f"SOCKS5 {proxy_host}:{proxy_port}",
                "auth": f"用户名: {username}",
                "user_agent": user_agent or "默认"
            }
            
            browser = await zd.start(
                headless=True,
                user_agent=user_agent,
                browser_args=browser_args,
                browser_connection_timeout=5.0
            )
            
            print(f"📋 配置信息: {config_info}")
            
            tab = await browser.get(self.test_urls['ip_check'])
            await tab.wait_for_ready_state("complete")
            
            # 获取 IP 信息
            ip_text = await tab.evaluate("document.body.innerText")
            ip_data = json.loads(ip_text)
            self.print_result("认证代理后的 IP 地址", ip_data)
            
            # 测试完整的请求信息
            await tab.get(self.test_urls['headers_check'])
            await tab.wait_for_ready_state("complete")
            headers_text = await tab.evaluate("document.body.innerText")
            headers_data = json.loads(headers_text)
            self.print_result("完整请求信息", headers_data)
            
            await browser.stop()
            return True
            
        except Exception as e:
            self.print_result("认证 SOCKS 代理测试失败", str(e), False)
            return False

    async def test_user_agent_variations(self):
        """测试不同的 User-Agent 设置"""
        self.print_section("测试 4: 多种 User-Agent 测试")
        
        results = {}
        
        for ua_name, ua_string in self.test_user_agents.items():
            try:
                print(f"🔄 测试 {ua_name}...")
                
                browser = await zd.start(
                    headless=True,
                    user_agent=ua_string
                )
                
                tab = await browser.get(self.test_urls['user_agent_check'])
                await tab.wait_for_ready_state("complete")
                
                ua_text = await tab.evaluate("document.body.innerText")
                ua_data = json.loads(ua_text)
                
                results[ua_name] = {
                    "设置的UA": ua_string[:50] + "..." if len(ua_string) > 50 else ua_string,
                    "检测到的UA": ua_data['user-agent'][:50] + "..." if len(ua_data['user-agent']) > 50 else ua_data['user-agent'],
                    "匹配": ua_string == ua_data['user-agent']
                }
                
                await browser.stop()
                
            except Exception as e:
                results[ua_name] = {"错误": str(e)}
        
        self.print_result("User-Agent 测试结果", results)
        return results

    async def run_comprehensive_test(self, proxy_config: Optional[Dict] = None):
        """运行综合测试"""
        print("🚀 Zendriver SOCKS 代理和 User-Agent 测试开始")
        print(f"📅 测试时间: {asyncio.get_event_loop().time()}")
        
        # 测试 1: 无代理
        await self.test_without_proxy(self.test_user_agents['chrome_windows'])
        
        # 测试 2: User-Agent 变化测试
        await self.test_user_agent_variations()
        
        # 测试 3 & 4: 代理测试（如果提供了代理配置）
        if proxy_config:
            if proxy_config.get('username') and proxy_config.get('password'):
                await self.test_socks_proxy_with_auth(
                    proxy_config['host'],
                    proxy_config['port'],
                    proxy_config['username'],
                    proxy_config['password'],
                    self.test_user_agents['firefox_linux']
                )
            else:
                await self.test_socks_proxy_no_auth(
                    proxy_config['host'],
                    proxy_config['port'],
                    self.test_user_agents['safari_mac']
                )
        else:
            print("\n⚠️  未提供代理配置，跳过代理测试")
            print("💡 要测试代理，请在 main() 函数中配置 proxy_config")
        
        self.print_section("测试完成")
        print("🎉 所有测试已完成！")


async def main():
    """主函数"""
    demo = ProxyUATestDemo()
    
    # 代理配置示例 - 请根据你的实际代理信息修改
    proxy_config = {
        'host': '127.0.0.1',        # 代理服务器地址
        'port': '1080',             # 代理端口
        'username': 'your_user',    # 用户名（如果需要认证）
        'password': 'your_pass'     # 密码（如果需要认证）
    }
    
    # 如果不需要测试代理，设置为 None
    # proxy_config = None
    
    try:
        await demo.run_comprehensive_test(proxy_config)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    # Windows 系统推荐使用这个事件循环策略
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())
