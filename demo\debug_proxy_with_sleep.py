#!/usr/bin/env python3
"""
代理调试版本 - 包含详细的 sleep 和调试信息
"""

import asyncio
import json
import sys
import requests
import zendriver as zd


def get_current_ip():
    """使用 requests 获取当前 IP 地址"""
    try:
        response = requests.get("https://httpbin.org/ip", timeout=10)
        return response.json()["origin"]
    except Exception as e:
        return f"获取失败: {e}"


async def debug_proxy_with_sleep():
    """带详细调试信息的代理测试"""
    
    # 代理配置
    PROXY_URL = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:10018"
    CUSTOM_UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 ZendriverDebug/1.0"
    
    print("🔍 代理调试测试 - 详细版本")
    print(f"🌐 代理: {PROXY_URL}")
    print(f"🎭 UA: {CUSTOM_UA}")
    print("=" * 60)
    
    # 获取本机 IP
    print("📍 步骤 1: 获取本机 IP...")
    original_ip = get_current_ip()
    print(f"   本机 IP: {original_ip}")
    print("⏳ 等待 2 秒...")
    await asyncio.sleep(2)
    
    try:
        # 启动浏览器
        print("\n🚀 步骤 2: 启动浏览器...")
        print(f"   配置参数: --proxy-server={PROXY_URL}")
        print(f"   User-Agent: {CUSTOM_UA}")
        print(f"   无头模式: False (可视化调试)")
        
        browser = await zd.start(
            headless=False,
            user_agent=CUSTOM_UA,
            browser_args=[f"--proxy-server={PROXY_URL}"],
            browser_connection_timeout=15.0
        )
        
        print("✅ 浏览器启动成功")
        print("⏳ 等待 5 秒，请观察浏览器窗口...")
        await asyncio.sleep(5)
        
        # 访问测试页面
        print("\n🌐 步骤 3: 访问测试页面...")
        print("   目标 URL: https://httpbin.org/ip")
        
        tab = await browser.get("https://httpbin.org/ip")
        print("✅ 页面请求已发送")
        print("⏳ 等待 5 秒，观察页面加载情况...")
        await asyncio.sleep(5)
        
        print("⏳ 等待页面完全加载...")
        await tab.wait_for_ready_state("complete")
        print("✅ 页面加载状态: complete")
        print("⏳ 等待 3 秒，观察最终页面内容...")
        await asyncio.sleep(3)
        
        # 获取页面内容
        print("\n📄 步骤 4: 获取页面内容...")
        page_content = await tab.evaluate("document.body.innerText")
        
        print(f"📄 页面内容长度: {len(page_content)} 字符")
        print(f"📄 页面内容预览: {repr(page_content[:100])}")
        
        if len(page_content) > 100:
            print(f"📄 页面内容完整: {repr(page_content)}")
        
        print("⏳ 等待 3 秒，分析页面内容...")
        await asyncio.sleep(3)
        
        # 分析页面内容
        print("\n🔍 步骤 5: 分析页面内容...")
        
        if "ERR_NO_SUPPORTED_PROXIES" in page_content:
            print("❌ 检测到代理错误: ERR_NO_SUPPORTED_PROXIES")
            print("   这表明 Chrome 不支持该代理或代理不可用")
        elif "无法访问此网站" in page_content:
            print("❌ 检测到网站访问错误")
            print("   代理可能无法连接到目标网站")
        elif page_content.strip().startswith("{"):
            print("✅ 检测到 JSON 格式内容，尝试解析...")
            try:
                ip_data = json.loads(page_content)
                proxy_ip = ip_data.get('origin', 'Unknown')
                print(f"✅ 解析成功！代理 IP: {proxy_ip}")
                
                if proxy_ip != original_ip:
                    print("🎉 代理工作正常！IP 已改变")
                else:
                    print("⚠️  IP 未改变，代理可能未生效")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
        else:
            print("⚠️  页面内容格式未知")
            print("   可能是错误页面或特殊响应")
        
        print("⏳ 等待 5 秒，然后关闭浏览器...")
        await asyncio.sleep(5)
        
        await browser.stop()
        print("✅ 浏览器已关闭")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        print(f"   错误类型: {type(e).__name__}")
        print("⏳ 等待 3 秒...")
        await asyncio.sleep(3)


if __name__ == "__main__":
    print("🐱 Zendriver 代理调试工具 - 详细版本")
    print("=" * 60)
    
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(debug_proxy_with_sleep())
