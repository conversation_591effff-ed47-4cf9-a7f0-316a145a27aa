#!/usr/bin/env python3
"""
简化版代理和 User-Agent 测试 Demo

Bug 记录:
- 使用提供的代理时出现 "ERR_NO_SUPPORTED_PROXIES" 错误
- Chrome 可能不支持该代理格式或代理服务器有问题
- 无代理情况下工作正常，User-Agent 设置成功
"""

import asyncio
import json
import sys
import requests
import zendriver as zd


def get_current_ip():
    """使用 requests 获取当前 IP 地址"""
    try:
        response = requests.get("https://httpbin.org/ip", timeout=10)
        return response.json()["origin"]
    except Exception as e:
        return f"获取失败: {e}"


async def test_proxy_and_ua():
    """测试代理和 User-Agent 配置"""
    
    # ==================== 配置区域 ====================
    # 🔧 代理配置 - 修改这里测试不同代理
    
    # 选择要测试的代理（取消注释其中一个）
    PROXY_URL = "http://0465846b31e1f583b17c__cr.us:<EMAIL>:10018"
    # PROXY_URL = "socks5://0465846b31e1f583b17c__cr.us:<EMAIL>:10019"
    # PROXY_URL = "https://0465846b31e1f583b17c__cr.us:<EMAIL>:10020"
    
    # 是否使用代理（设为 False 测试无代理情况）
    USE_PROXY = True
    
    # 自定义 User-Agent
    CUSTOM_UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 ZendriverDemo/1.0"
    
    # ==================== 测试开始 ====================
    
    print("🚀 Zendriver 代理和 UA 测试")
    print(f"🎭 User-Agent: {CUSTOM_UA}")
    print("=" * 50)
    
    # 先获取本机 IP
    print("📍 获取本机 IP...")
    original_ip = get_current_ip()
    print(f"   本机 IP: {original_ip}")
    print()
    
    try:
        # 构建浏览器参数
        browser_args = []
        if USE_PROXY:
            browser_args.append(f"--proxy-server={PROXY_URL}")
            print(f"🌐 使用代理: {PROXY_URL}")
        else:
            print("🚫 不使用代理")
        
        print()
        
        # 启动浏览器
        print("🚀 启动浏览器...")
        browser = await zd.start(
            headless=True,
            user_agent=CUSTOM_UA,
            browser_args=browser_args,
            browser_connection_timeout=15.0
        )
        
        # 测试 IP 地址
        print("📍 检查浏览器 IP...")
        tab = await browser.get("https://httpbin.org/ip")
        await tab.wait_for_ready_state("complete")
        
        ip_text = await tab.evaluate("document.body.innerText")
        ip_data = json.loads(ip_text)
        browser_ip = ip_data['origin']
        print(f"   浏览器 IP: {browser_ip}")
        
        # 比较 IP
        if USE_PROXY:
            if browser_ip != original_ip:
                print("   ✅ 代理工作正常！IP 已改变")
            else:
                print("   ⚠️  代理可能未生效，IP 未改变")
        else:
            print("   ℹ️  未使用代理")
        
        # 测试 User-Agent
        print("🎭 检查 User-Agent...")
        await tab.get("https://httpbin.org/user-agent")
        await tab.wait_for_ready_state("complete")
        
        ua_text = await tab.evaluate("document.body.innerText")
        ua_data = json.loads(ua_text)
        detected_ua = ua_data['user-agent']
        print(f"   检测到的 UA: {detected_ua}")
        
        # 验证 UA
        if CUSTOM_UA == detected_ua:
            print("   ✅ User-Agent 设置成功")
        else:
            print("   ⚠️  User-Agent 可能未完全匹配")
        
        await browser.stop()
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("💡 可能的问题:")
        print("   1. 代理服务器不可用或已过期")
        print("   2. 代理认证信息错误")
        print("   3. 网络连接问题")
        print("   4. Zendriver 代理配置方法需要调整")


if __name__ == "__main__":
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(test_proxy_and_ua())
