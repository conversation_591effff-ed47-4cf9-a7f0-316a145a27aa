name: lint
on:
  push:
    branches:
      - main
    paths:
      - "**.py"
  pull_request:
    branches:
      - main
    paths:
      - "**.py"
jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v3
      - name: Set up Python
        run: uv python install
      - name: Install dependencies
        run: uv sync --all-extras --dev
      - name: Lint with ruff/mypy
        run: /bin/bash ./scripts/lint.sh
