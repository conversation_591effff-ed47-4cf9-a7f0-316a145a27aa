name: docs
on:
  push:
    branches:
      - main
    paths:
      - "docs/**"
      # Files which are used during doc generation
      - "mkdocs.yml"
      - "README.md"
      - "CHANGELOG.md"
      - "zendriver/cdp/**"
  workflow_dispatch:
permissions:
  contents: write
jobs:
  docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure Git Credentials
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com
      - name: Install uv
        uses: astral-sh/setup-uv@v3
      - name: Set up Python
        run: uv python install
      - name: Install dependencies
        run: uv sync --all-extras --dev
      - name: Auto-generate docs
        run: |
          uv run python scripts/mkdocs_generate_reference.py
          uv run python scripts/mkdocs_generate_release_notes.py
      - name: Deploy to GitHub Pages
        run: uv run mkdocs gh-deploy --force
