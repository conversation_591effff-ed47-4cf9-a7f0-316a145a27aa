#!/usr/bin/env python3
"""
代理测试调试版本 - 提供详细的错误信息
"""

import asyncio
import json
import sys
import zendriver as zd


async def test_proxy_with_debug(proxy_name, proxy_url):
    """测试单个代理并提供详细调试信息"""
    
    print(f"\n🔍 测试 {proxy_name}")
    print(f"🌐 代理: {proxy_url}")
    print("-" * 50)
    
    browser = None
    try:
        print("🚀 启动浏览器...")
        browser = await zd.start(
            headless=True,
            browser_args=[
                f"--proxy-server={proxy_url}",
                "--disable-web-security",  # 禁用安全检查
                "--ignore-certificate-errors",  # 忽略证书错误
                "--ignore-ssl-errors",  # 忽略SSL错误
                "--disable-features=VizDisplayCompositor"  # 禁用某些功能
            ],
            browser_connection_timeout=15.0
        )
        print("✅ 浏览器启动成功")
        
        print("🌐 尝试连接测试网站...")
        tab = await browser.get("https://httpbin.org/ip")
        print("✅ 页面加载成功")
        
        print("⏳ 等待页面完全加载...")
        await tab.wait_for_ready_state("complete")
        print("✅ 页面加载完成")
        
        print("📄 获取页面内容...")
        # 先检查页面是否有内容
        page_content = await tab.evaluate("document.body ? document.body.innerText : 'No content'")
        print(f"📄 页面内容长度: {len(page_content)} 字符")
        
        if len(page_content) < 10:
            print("⚠️  页面内容太少，可能加载失败")
            print(f"页面内容: {page_content}")
            return False
        
        # 尝试解析 JSON
        try:
            ip_data = json.loads(page_content)
            print(f"✅ IP 地址: {ip_data.get('origin', 'Unknown')}")
            print(f"✅ {proxy_name} 代理工作正常！")
            return True
        except json.JSONDecodeError as e:
            print(f"❌ JSON 解析失败: {e}")
            print(f"原始内容: {page_content[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ {proxy_name} 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False
    finally:
        if browser:
            try:
                await browser.stop()
                print("🔄 浏览器已关闭")
            except:
                pass


async def main():
    """主测试函数"""
    
    print("🐱 Zendriver 代理调试测试工具")
    print("=" * 60)
    
    # 代理列表
    proxies = [
        ("HTTP 代理", "http://0465846b31e1f583b17c__cr.us:<EMAIL>:10018"),
        ("SOCKS5 代理", "socks5://0465846b31e1f583b17c__cr.us:<EMAIL>:10019"),
        ("HTTPS 代理", "https://0465846b31e1f583b17c__cr.us:<EMAIL>:10020")
    ]
    
    results = {}
    
    for proxy_name, proxy_url in proxies:
        success = await test_proxy_with_debug(proxy_name, proxy_url)
        results[proxy_name] = success
        
        # 在测试之间稍作等待
        await asyncio.sleep(2)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("-" * 30)
    
    for proxy_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{proxy_name}: {status}")
    
    successful_count = sum(results.values())
    print(f"\n🎯 成功率: {successful_count}/{len(proxies)} ({successful_count/len(proxies)*100:.1f}%)")
    
    if successful_count == 0:
        print("\n💡 所有代理都失败了，可能的原因:")
        print("   1. 代理服务器不可用或已过期")
        print("   2. 网络连接问题")
        print("   3. 代理认证信息错误")
        print("   4. 防火墙阻止连接")
        print("   5. 代理服务器需要特殊配置")


if __name__ == "__main__":
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())
