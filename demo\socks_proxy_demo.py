#!/usr/bin/env python3
"""
SOCKS 代理测试 Demo
"""

import asyncio
import json
import sys
from urllib.parse import quote
import zendriver as zd


async def test_socks_proxy():
    """测试 SOCKS 代理配置"""
    
    # ==================== 代理配置 ====================
    PROXY_HOST = "127.0.0.1"
    PROXY_PORT = "1080"
    PROXY_USERNAME = ""  # 如需认证请填写用户名
    PROXY_PASSWORD = ""  # 如需认证请填写密码
    
    print("🌐 SOCKS 代理测试")
    print("-" * 30)
    
    try:
        # 构建代理 URL
        if PROXY_USERNAME and PROXY_PASSWORD:
            # 带认证
            encoded_username = quote(PROXY_USERNAME)
            encoded_password = quote(PROXY_PASSWORD)
            proxy_url = f"socks5://{encoded_username}:{encoded_password}@{PROXY_HOST}:{PROXY_PORT}"
            print(f"🔐 代理: {PROXY_USERNAME}@{PROXY_HOST}:{PROXY_PORT}")
        else:
            # 无认证
            proxy_url = f"socks5://{PROXY_HOST}:{PROXY_PORT}"
            print(f"🔓 代理: {PROXY_HOST}:{PROXY_PORT}")
        
        print()
        
        # 启动浏览器
        browser = await zd.start(
            headless=True,
            browser_args=[f"--proxy-server={proxy_url}"]
        )
        
        # 检查 IP 地址
        print("📍 检查 IP 地址...")
        tab = await browser.get("https://httpbin.org/ip")
        await tab.wait_for_ready_state("complete")
        
        ip_text = await tab.evaluate("document.body.innerText")
        ip_data = json.loads(ip_text)
        
        print(f"当前 IP: {ip_data['origin']}")
        print("✅ 代理连接成功！")
        
        await browser.stop()
        
    except Exception as e:
        print(f"❌ 代理连接失败: {e}")
        print("💡 请检查代理服务器是否运行正常")


if __name__ == "__main__":
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(test_socks_proxy())
