#!/usr/bin/env python3
"""
快速 SOCKS 代理和 UA 测试脚本
简化版本，适合快速验证配置
"""

import asyncio
import json
import sys
from urllib.parse import quote
import zendriver as zd


async def quick_test():
    """快速测试函数"""
    
    # ==================== 配置区域 ====================
    # 🔧 请根据你的实际情况修改以下配置
    
    # SOCKS 代理配置
    PROXY_CONFIG = {
        'enabled': True,           # 是否启用代理测试
        'host': '127.0.0.1',      # 代理服务器地址
        'port': '1080',           # 代理端口
        'username': '',           # 用户名（留空表示无认证）
        'password': ''            # 密码（留空表示无认证）
    }
    
    # User-Agent 配置
    CUSTOM_UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 ZendriverTest/1.0"
    
    # 测试网址
    TEST_URLS = {
        'ip': 'https://httpbin.org/ip',
        'headers': 'https://httpbin.org/headers'
    }
    
    # ==================== 测试开始 ====================
    
    print("🚀 开始快速测试...")
    print(f"🔧 代理配置: {'启用' if PROXY_CONFIG['enabled'] else '禁用'}")
    print(f"🌐 User-Agent: {CUSTOM_UA[:50]}...")
    print("-" * 60)
    
    try:
        # 构建浏览器参数
        browser_args = []
        
        if PROXY_CONFIG['enabled']:
            if PROXY_CONFIG['username'] and PROXY_CONFIG['password']:
                # 带认证的代理
                encoded_username = quote(PROXY_CONFIG['username'])
                encoded_password = quote(PROXY_CONFIG['password'])
                proxy_url = f"socks5://{encoded_username}:{encoded_password}@{PROXY_CONFIG['host']}:{PROXY_CONFIG['port']}"
                print(f"🔐 使用认证代理: {PROXY_CONFIG['username']}@{PROXY_CONFIG['host']}:{PROXY_CONFIG['port']}")
            else:
                # 无认证代理
                proxy_url = f"socks5://{PROXY_CONFIG['host']}:{PROXY_CONFIG['port']}"
                print(f"🌐 使用代理: {PROXY_CONFIG['host']}:{PROXY_CONFIG['port']}")
            
            browser_args.append(f"--proxy-server={proxy_url}")
        
        # 启动浏览器
        print("🌟 启动浏览器...")
        browser = await zd.start(
            headless=True,
            user_agent=CUSTOM_UA,
            browser_args=browser_args,
            browser_connection_timeout=5.0
        )
        
        # 测试 IP 地址
        print("🔍 检查 IP 地址...")
        tab = await browser.get(TEST_URLS['ip'])
        await tab.wait_for_ready_state("complete")
        
        ip_text = await tab.evaluate("document.body.innerText")
        ip_data = json.loads(ip_text)
        print(f"✅ 当前 IP: {ip_data['origin']}")
        
        # 测试请求头
        print("🔍 检查请求头...")
        await tab.get(TEST_URLS['headers'])
        await tab.wait_for_ready_state("complete")
        
        headers_text = await tab.evaluate("document.body.innerText")
        headers_data = json.loads(headers_text)
        
        print("✅ 请求头信息:")
        print(f"   User-Agent: {headers_data['headers'].get('User-Agent', 'N/A')}")
        print(f"   Accept: {headers_data['headers'].get('Accept', 'N/A')}")
        print(f"   Accept-Language: {headers_data['headers'].get('Accept-Language', 'N/A')}")
        
        # 验证 User-Agent 是否正确设置
        detected_ua = headers_data['headers'].get('User-Agent', '')
        if CUSTOM_UA in detected_ua:
            print("✅ User-Agent 设置成功！")
        else:
            print("⚠️  User-Agent 可能未正确设置")
            print(f"   期望: {CUSTOM_UA}")
            print(f"   实际: {detected_ua}")
        
        await browser.stop()
        print("🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("💡 请检查:")
        print("   1. 代理服务器是否正常运行")
        print("   2. 代理地址和端口是否正确")
        print("   3. 用户名密码是否正确（如果需要认证）")
        print("   4. 网络连接是否正常")


async def test_multiple_configs():
    """测试多种配置的对比"""
    
    print("🔄 多配置对比测试...")
    
    configs = [
        {
            'name': '无代理 + 默认UA',
            'proxy': None,
            'ua': None
        },
        {
            'name': '无代理 + 自定义UA',
            'proxy': None,
            'ua': 'ZendriverTest/1.0 Custom Browser'
        },
        {
            'name': 'SOCKS代理 + 自定义UA',
            'proxy': 'socks5://127.0.0.1:1080',
            'ua': 'ZendriverTest/1.0 Proxy Browser'
        }
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n📋 测试 {i}: {config['name']}")
        print("-" * 40)
        
        try:
            browser_args = []
            if config['proxy']:
                browser_args.append(f"--proxy-server={config['proxy']}")
            
            browser = await zd.start(
                headless=True,
                user_agent=config['ua'],
                browser_args=browser_args,
                browser_connection_timeout=3.0
            )
            
            tab = await browser.get('https://httpbin.org/ip')
            await tab.wait_for_ready_state("complete")
            
            ip_text = await tab.evaluate("document.body.innerText")
            ip_data = json.loads(ip_text)
            
            print(f"✅ IP: {ip_data['origin']}")
            
            if config['ua']:
                await tab.get('https://httpbin.org/user-agent')
                await tab.wait_for_ready_state("complete")
                ua_text = await tab.evaluate("document.body.innerText")
                ua_data = json.loads(ua_text)
                print(f"✅ UA: {ua_data['user-agent']}")
            
            await browser.stop()
            
        except Exception as e:
            print(f"❌ 配置 {i} 测试失败: {e}")


if __name__ == "__main__":
    print("🐱 Zendriver 快速代理和 UA 测试工具")
    print("=" * 60)
    
    # Windows 兼容性设置
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 快速测试（推荐）")
    print("2. 多配置对比测试")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            asyncio.run(quick_test())
        elif choice == "2":
            asyncio.run(test_multiple_configs())
        else:
            print("❌ 无效选择，运行默认快速测试")
            asyncio.run(quick_test())
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 运行错误: {e}")
