#!/usr/bin/env python3
"""
User-Agent 设置测试 Demo
"""

import asyncio
import json
import sys
import zendriver as zd


async def test_user_agent():
    """测试 User-Agent 设置"""
    
    # 测试用的 User-Agent
    test_ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 ZendriverMobile/1.0"
    
    print("🎭 User-Agent 测试")
    print("-" * 40)
    print(f"设置的 UA: {test_ua}")
    print()
    
    try:
        # 启动浏览器并设置 User-Agent
        browser = await zd.start(
            headless=True,
            user_agent=test_ua
        )
        
        # 访问测试网站
        tab = await browser.get("https://httpbin.org/user-agent")
        await tab.wait_for_ready_state("complete")
        
        # 获取检测到的 User-Agent
        ua_text = await tab.evaluate("document.body.innerText")
        ua_data = json.loads(ua_text)
        detected_ua = ua_data['user-agent']
        
        print(f"检测到的 UA: {detected_ua}")
        print()
        
        # 验证结果
        if test_ua == detected_ua:
            print("✅ User-Agent 设置成功！")
        else:
            print("❌ User-Agent 设置失败")
        
        await browser.stop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(test_user_agent())
