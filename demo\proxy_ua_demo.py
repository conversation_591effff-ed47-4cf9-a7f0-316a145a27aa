#!/usr/bin/env python3
"""
简化版 SOCKS 代理和 User-Agent 测试 Demo
"""

import asyncio
import json
import sys
import zendriver as zd


async def test_proxy_and_ua():
    """测试代理和 User-Agent 配置"""
    
    # ==================== 配置区域 ====================
    # 🔧 测试多种代理类型

    # 代理列表 - 会依次测试每个代理
    PROXY_LIST = [
        {
            'name': 'HTTP 代理',
            'url': 'http://0465846b31e1f583b17c__cr.us:<EMAIL>:10018'
        },
        {
            'name': 'SOCKS5 代理',
            'url': 'socks5://0465846b31e1f583b17c__cr.us:<EMAIL>:10019'
        },
        {
            'name': 'HTTPS 代理',
            'url': 'https://0465846b31e1f583b17c__cr.us:<EMAIL>:10020'
        }
    ]

    # 自定义 User-Agent
    CUSTOM_UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 ZendriverDemo/1.0"
    
    # ==================== 测试开始 ====================

    print("🚀 Zendriver 多代理类型测试")
    print(f"🎭 User-Agent: {CUSTOM_UA}")
    print("=" * 60)

    # 依次测试每个代理
    for i, proxy_config in enumerate(PROXY_LIST, 1):
        print(f"\n� 测试 {i}: {proxy_config['name']}")
        print(f"🌐 代理地址: {proxy_config['url']}")
        print("-" * 40)

        try:
            # 启动浏览器
            browser = await zd.start(
                headless=True,
                user_agent=CUSTOM_UA,
                browser_args=[f"--proxy-server={proxy_config['url']}"],
                browser_connection_timeout=10.0  # 增加超时时间
            )

            # 测试 IP 地址
            print("📍 检查 IP 地址...")
            tab = await browser.get("https://httpbin.org/ip")
            await tab.wait_for_ready_state("complete")

            ip_text = await tab.evaluate("document.body.innerText")
            ip_data = json.loads(ip_text)
            print(f"   当前 IP: {ip_data['origin']}")

            # 测试 User-Agent
            print("🎭 检查 User-Agent...")
            await tab.get("https://httpbin.org/user-agent")
            await tab.wait_for_ready_state("complete")

            ua_text = await tab.evaluate("document.body.innerText")
            ua_data = json.loads(ua_text)
            detected_ua = ua_data['user-agent']
            print(f"   检测到的 UA: {detected_ua[:80]}...")

            # 验证 UA 是否匹配
            if CUSTOM_UA == detected_ua:
                print("   ✅ User-Agent 设置成功")
            else:
                print("   ⚠️  User-Agent 可能未完全匹配")

            print(f"   ✅ {proxy_config['name']} 连接成功！")

            await browser.stop()

        except Exception as e:
            print(f"   ❌ {proxy_config['name']} 连接失败: {e}")
            print("   💡 请检查代理服务器状态")

    print("\n🎉 所有代理测试完成！")


if __name__ == "__main__":
    # Windows 兼容性设置
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(test_proxy_and_ua())
