#!/usr/bin/env python3
"""
简化版 SOCKS 代理和 User-Agent 测试 Demo
"""

import asyncio
import json
import sys
from urllib.parse import quote
import zendriver as zd


async def test_proxy_and_ua():
    """测试代理和 User-Agent 配置"""
    
    # ==================== 配置区域 ====================
    # 🔧 请根据实际情况修改以下配置
    
    # SOCKS 代理配置
    PROXY_HOST = "127.0.0.1"
    PROXY_PORT = "1080"
    PROXY_USERNAME = ""  # 留空表示无认证
    PROXY_PASSWORD = ""  # 留空表示无认证
    
    # 自定义 User-Agent
    CUSTOM_UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 ZendriverDemo/1.0"
    
    # 是否启用代理测试
    USE_PROXY = True
    
    # ==================== 测试开始 ====================
    
    print("🚀 Zendriver 代理和 UA 测试")
    print("-" * 50)
    
    try:
        # 构建浏览器参数
        browser_args = []
        
        if USE_PROXY:
            if PROXY_USERNAME and PROXY_PASSWORD:
                # 带认证的 SOCKS 代理
                encoded_username = quote(PROXY_USERNAME)
                encoded_password = quote(PROXY_PASSWORD)
                proxy_url = f"socks5://{encoded_username}:{encoded_password}@{PROXY_HOST}:{PROXY_PORT}"
                print(f"🔐 使用认证代理: {PROXY_USERNAME}@{PROXY_HOST}:{PROXY_PORT}")
            else:
                # 无认证 SOCKS 代理
                proxy_url = f"socks5://{PROXY_HOST}:{PROXY_PORT}"
                print(f"🌐 使用代理: {PROXY_HOST}:{PROXY_PORT}")
            
            browser_args.append(f"--proxy-server={proxy_url}")
        else:
            print("🚫 不使用代理")
        
        print(f"🎭 User-Agent: {CUSTOM_UA}")
        print()
        
        # 启动浏览器
        browser = await zd.start(
            headless=True,
            user_agent=CUSTOM_UA,
            browser_args=browser_args
        )
        
        # 测试 IP 地址
        print("📍 检查 IP 地址...")
        tab = await browser.get("https://httpbin.org/ip")
        await tab.wait_for_ready_state("complete")
        
        ip_text = await tab.evaluate("document.body.innerText")
        ip_data = json.loads(ip_text)
        print(f"   当前 IP: {ip_data['origin']}")
        
        # 测试 User-Agent
        print("🎭 检查 User-Agent...")
        await tab.get("https://httpbin.org/user-agent")
        await tab.wait_for_ready_state("complete")
        
        ua_text = await tab.evaluate("document.body.innerText")
        ua_data = json.loads(ua_text)
        detected_ua = ua_data['user-agent']
        print(f"   检测到的 UA: {detected_ua}")
        
        # 验证 UA 是否匹配
        if CUSTOM_UA == detected_ua:
            print("   ✅ User-Agent 设置成功")
        else:
            print("   ⚠️  User-Agent 可能未完全匹配")
        
        await browser.stop()
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("💡 请检查代理配置和网络连接")


if __name__ == "__main__":
    # Windows 兼容性设置
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(test_proxy_and_ua())
