The following tutorials demonstrate how to use Zen<PERSON>iver for a variety of real-world web automation tasks, including

- Navigating an infinitely scrolling feed
- Creating user accounts
- Retrieving the results of API requests
- (Soon) Many more!

The tutorials in this documentation use [example pages](https://github.com/cdpdriver/examples) created expressly for demonstration purposes. None of the tutorials send real data or create real accounts on real websites.

Always exercise caution when applying these techniques to automating live sites. Be a good web citizen! Ensure you do not make excessive numbers of requests or become a burden for the website owners, or you will quickly find your requests blocked and your IP banned.

<figure markdown="span">
![Movie screenshot: with great power comes great responsibility](./great-power.jpg)
</figure>
