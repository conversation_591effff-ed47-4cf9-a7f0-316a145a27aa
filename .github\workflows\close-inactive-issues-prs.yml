name: Close inactive issues
on:
  schedule:
    - cron: "30 1 * * *"
  workflow_dispatch:
jobs:
  close-issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: 30
          days-before-issue-close: 7
          stale-issue-label: "stale"
          stale-issue-message: "This issue has been marked stale because it has been open for 30 days with no activity. If there is no activity within 7 days, it will be automatically closed."
          close-issue-message: "This issue was automatically closed because it has been inactive for 7 days since being marked as stale."
          only-issue-labels: "question"
          days-before-pr-stale: 30
          days-before-pr-close: 7
          stale-pr-label: "stale"
          stale-pr-message: "This pull request has been marked stale because it has been open for 30 days with no activity. If there is no activity within 7 days, it will be automatically closed."
          close-pr-message: "This pull request was automatically closed because it has been inactive for 7 days since being marked as stale."
          repo-token: ${{ secrets.GITHUB_TOKEN }}
