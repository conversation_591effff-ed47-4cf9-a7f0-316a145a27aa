# Zendriver Demo 示例

这个文件夹包含了 Zendriver 的简单使用示例。

## 文件说明

### `simple_proxy_test.py` - 代理和 UA 测试
简化版的代理配置和 User-Agent 测试工具。

**功能：**
- 使用 requests 获取本机 IP 作为对比
- 测试代理是否生效（通过 IP 变化判断）
- 验证 User-Agent 设置
- 提供详细的错误诊断信息

**使用方法：**
1. 修改文件中的代理配置
2. 运行：`python simple_proxy_test.py`

### `test_zendriver_features.py` - pytest 测试套件 ✨
完整的 pytest 测试套件，包含多个独立测试用例。

**测试用例：**
- `test_user_agent_only` - 纯 User-Agent 功能测试
- `test_ip_check_no_proxy` - 无代理 IP 获取测试
- `test_proxy_connection` - 代理连接测试（预期失败）
- `test_combined_ua_and_ip` - 综合功能测试

**运行方法：**
```bash
# 运行所有测试
python test_zendriver_features.py

# 或使用 pytest
pytest test_zendriver_features.py -v -s
```

**测试结果：** ✅ 3 passed, 1 skipped

## Bug 记录

### 代理配置问题 ✅ 已定位
- **问题**: 使用提供的代理地址时出现 "ERR_NO_SUPPORTED_PROXIES" 错误
- **具体错误**: Chrome 返回 "无法访问此网站" 和 "ERR_NO_SUPPORTED_PROXIES"
- **测试结果**:
  - ❌ 代理模式: 返回错误页面
  - ✅ 无代理模式: 正常工作，返回正确的 JSON 数据
  - ✅ User-Agent 设置: 在无代理模式下验证成功
- **结论**:
  1. 提供的代理可能不可用或格式不被 Chrome 支持
  2. Zendriver 的 User-Agent 配置功能正常
  3. 代理配置语法正确，但代理服务器有问题

### 配置验证
- **Config 类的 `port` 参数**: 经确认是用于 Chrome 调试端口，不是代理配置
- **正确的代理配置方法**: 通过 `browser_args` 传入 `--proxy-server=URL`

## 配置说明

### SOCKS 代理配置
```python
PROXY_HOST = "127.0.0.1"      # 代理服务器地址
PROXY_PORT = "1080"           # 代理端口
PROXY_USERNAME = ""           # 用户名（无认证时留空）
PROXY_PASSWORD = ""           # 密码（无认证时留空）
```

### User-Agent 配置
```python
CUSTOM_UA = "Your Custom User Agent String"
```

## 运行要求

- Python 3.8+
- zendriver 库：`pip install zendriver`

## 注意事项

- 代理测试需要有可用的 SOCKS 代理服务器
- 测试使用 httpbin.org 作为测试网站
- Windows 用户会自动使用兼容的事件循环策略
