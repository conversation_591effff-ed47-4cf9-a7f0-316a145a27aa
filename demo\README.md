# Zendriver Demo 示例

这个文件夹包含了 Zendriver 的简单使用示例。

## 文件说明

### 1. `proxy_ua_demo.py` - 代理和 UA 综合测试
测试 SOCKS 代理配置和 User-Agent 设置。

**使用方法：**
1. 修改文件中的代理配置
2. 运行：`python proxy_ua_demo.py`

### 2. `user_agent_demo.py` - User-Agent 测试
单独测试 User-Agent 设置功能。

**使用方法：**
直接运行：`python user_agent_demo.py`

### 3. `socks_proxy_demo.py` - SOCKS 代理测试
单独测试 SOCKS 代理连接功能。

**使用方法：**
1. 修改文件中的代理配置
2. 运行：`python socks_proxy_demo.py`

## 配置说明

### SOCKS 代理配置
```python
PROXY_HOST = "127.0.0.1"      # 代理服务器地址
PROXY_PORT = "1080"           # 代理端口
PROXY_USERNAME = ""           # 用户名（无认证时留空）
PROXY_PASSWORD = ""           # 密码（无认证时留空）
```

### User-Agent 配置
```python
CUSTOM_UA = "Your Custom User Agent String"
```

## 运行要求

- Python 3.8+
- zendriver 库：`pip install zendriver`

## 注意事项

- 代理测试需要有可用的 SOCKS 代理服务器
- 测试使用 httpbin.org 作为测试网站
- Windows 用户会自动使用兼容的事件循环策略
