from unittest.mock import Mock

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from zendriver.core.browser import <PERSON>rowser


@pytest.fixture
def mock_print(mocker: MockerFixture) -> Mock:
    return mocker.patch("builtins.print")


@pytest.fixture
def mock_start(mocker: <PERSON><PERSON><PERSON><PERSON><PERSON>, browser: Browser) -> Mock:
    return mocker.patch("zendriver.start", return_value=browser)
